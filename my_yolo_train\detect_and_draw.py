from ultralytics import YOLO
import cv2
import numpy as np
import time
import math

# Model yolunu kendi modelinize göre ayarlayın
yolo_model_path = 'my_yolo_train/weights/best.pt'

# İHA parametreleri (bu değerleri gerçek değerlerle güncelleyin)
IHA_HEIGHT = 50.0  # İHA'nın yerden yüksekliği (metre)
IHA_HORIZONTAL_SPEED = 10.0  # İHA'nın yatay hızı (m/s)
WIND_SPEED = 2.0  # Rüzgar hızı (m/s) - İHA hızına zıt yönde
GRAVITY = 9.81  # Yerçekimi ivmesi (m/s²)

def calculate_drop_distance(h, v0):
    """
    Top bırakma mesafesini hesaplar
    h: İHA'nın yüksekliği (metre)
    v0: Topun yatay hızı (m/s)
    Returns: Yatay at<PERSON><PERSON> mesafesi (metre)
    """
    if h <= 0 or v0 <= 0:
        return 0
    return math.sqrt(2 * h * (v0 ** 2) / GRAVITY)

def pixels_to_meters(pixel_distance, frame_width, real_world_width=100):
    """
    Piksel mesafesini metre cinsine çevirir
    pixel_distance: Piksel cinsinden mesafe
    frame_width: Görüntü genişliği (piksel)
    real_world_width: Gerçek dünya genişliği (metre) - kameranın gördüğü alan
    """
    return (pixel_distance * real_world_width) / frame_width

# Modeli yükle
model = YOLO(yolo_model_path)

# Sınıf isimlerini modelden otomatik al
CLASS_NAMES = model.names
print('Modeldeki sınıf isimleri:', CLASS_NAMES)

# Kamera başlat
cap = cv2.VideoCapture(0)

first_frame = True
prev_time = time.time()
fps = 0

while True:
    ret, frame = cap.read()
    if not ret:
        print('Kamera görüntüsü alınamadı!')
        break

    # FPS hesapla
    curr_time = time.time()
    fps = 1 / (curr_time - prev_time)
    prev_time = curr_time

    img_height, img_width = frame.shape[:2]
    img_center = (img_width // 2, img_height // 2)

    # YOLO ile tahmin (konsol çıktısı kapalı)
    results = model(frame, verbose=False)[0]
    boxes = results.boxes.xyxy.cpu().numpy() if results.boxes.xyxy is not None else []
    classes = results.boxes.cls.cpu().numpy() if results.boxes.cls is not None else []

    if first_frame:
        print('Tespit edilen sınıf indeksleri:', classes)
        first_frame = False

    for i, box in enumerate(boxes):
        x1, y1, x2, y2 = map(int, box)
        cls = int(classes[i]) if i < len(classes) else 0
        label = CLASS_NAMES[cls] if cls < len(CLASS_NAMES) else str(cls)

        # ROI'yi al
        roi = frame[y1:y2, x1:x2]
        if roi.size == 0:
            continue

        # ROI'yi griye çevir ve threshold uygula
        gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 60, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Kontur bul
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            continue

        # En büyük konturu al
        cnt = max(contours, key=cv2.contourArea)
        area = cv2.contourArea(cnt)
        if area < 0.2 * roi.shape[0] * roi.shape[1]:  # Alan çok küçükse atla
            continue

        # Konturu yaklaşıkla
        approx = cv2.approxPolyDP(cnt, 0.04 * cv2.arcLength(cnt, True), True)
        if len(approx) == 4 and cv2.isContourConvex(approx):
            # Dört köşe ve konveks ise kare olabilir
            # Kenar uzunluklarını kontrol et
            edges = [np.linalg.norm(approx[i][0] - approx[(i+1)%4][0]) for i in range(4)]
            min_edge = min(edges)
            max_edge = max(edges)
            if min_edge / max_edge > 0.8:
                # Gerçekten kare!
                if label == 'kirmizi_kare':
                    color = (0, 0, 255)
                elif label == 'mavi_kare':
                    color = (255, 0, 0)
                else:
                    color = (0, 255, 255)
                # Orijinal frame'e çiz
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                cv2.putText(frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
                box_center = ((x1 + x2) // 2, (y1 + y2) // 2)
                # Koordinat sistemini merkez (0,0) olacak şekilde ayarla (yukarı +, aşağı -)
                rel_box_center = (box_center[0] - img_center[0], -(box_center[1] - img_center[1]))
                rel_img_center = (0, 0)
                # Doğruyu çiz
                cv2.line(frame, box_center, img_center, (0, 255, 0), 2)
                # Merkez noktalarını işaretle
                cv2.circle(frame, box_center, 5, color, -1)
                cv2.circle(frame, img_center, 5, (0, 255, 0), -1)
                # Koordinatları ve uzaklığı yaz
                dist = int(np.linalg.norm(np.array(rel_box_center)))
                cv2.putText(frame, f"{rel_box_center}", (box_center[0]+10, box_center[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                cv2.putText(frame, f"{rel_img_center}", (img_center[0]+10, img_center[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,255,0), 2)
                mid_point = ((box_center[0] + img_center[0]) // 2, (box_center[1] + img_center[1]) // 2)
                cv2.putText(frame, f"{dist}px", mid_point, cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,255,0), 2)

                # TOP BIRAKMA HESAPLAMASı
                # V0 hesapla (İHA hızı - rüzgar hızı)
                v0 = IHA_HORIZONTAL_SPEED - WIND_SPEED

                # Yatay atış mesafesini hesapla
                drop_distance_meters = calculate_drop_distance(IHA_HEIGHT, v0)

                # Hedef mesafesini piksel cinsinden metreye çevir
                target_distance_meters = pixels_to_meters(dist, img_width)

                # Top bırakma kararı
                drop_threshold = 5.0  # 5 metre tolerans
                should_drop = abs(target_distance_meters - drop_distance_meters) <= drop_threshold

                # Top bırakma bilgilerini ekranda göster
                drop_info_y = y1 - 40
                cv2.putText(frame, f"Hedef: {target_distance_meters:.1f}m", (x1, drop_info_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                cv2.putText(frame, f"Atış: {drop_distance_meters:.1f}m", (x1, drop_info_y - 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

                if should_drop:
                    cv2.putText(frame, "TOP BIRAK!", (x1, drop_info_y - 40),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                    print(f"🎯 TOP BIRAKMA KOMUTU! Hedef: {label}, Mesafe: {target_distance_meters:.1f}m")
                else:
                    fark = target_distance_meters - drop_distance_meters
                    if fark > 0:
                        cv2.putText(frame, f"Erken {fark:.1f}m", (x1, drop_info_y - 40),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
                    else:
                        cv2.putText(frame, f"Geç {abs(fark):.1f}m", (x1, drop_info_y - 40),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)

    # FPS ve İHA bilgilerini sol üst köşeye yaz
    cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0,255,0), 2)
    cv2.putText(frame, f"İHA Yükseklik: {IHA_HEIGHT}m", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,255), 2)
    cv2.putText(frame, f"İHA Hız: {IHA_HORIZONTAL_SPEED}m/s", (10, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,255), 2)
    cv2.putText(frame, f"Rüzgar: {WIND_SPEED}m/s", (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,255), 2)
    cv2.putText(frame, f"Net Hız: {IHA_HORIZONTAL_SPEED - WIND_SPEED}m/s", (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,255), 2)

    cv2.imshow('YOLO Gerçek Kare Tespiti', frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows() 


# ----------------------------------------------------------------------------------------------------------------------------


# from ultralytics import YOLO
# import cv2
# import numpy as np
# import time
# import math

# # ----- 1) Formül fonksiyonu -----
# def compute_drop_distance(h: float, v0: float, g: float = 9.81) -> float:
#     """
#     Yerden yükseklik h ve yatay hız v0 için
#     serbest düşme süresince kat edilen yatay mesafeyi (X) verir.
#     """
#     return v0 * math.sqrt(2 * h / g)

# # ----- 2) Sistem parametreleri (sizin kontrol biriminizden dinamik alınmalı) -----
# ALTITUDE_M = 20.0            # örnek: 20 metre
# HORIZONTAL_VELOCITY_MPS = 5.0  # örnek: 5 m/s
# METERS_PER_PIXEL_X = 0.02    # örnek: 2 cm/piksel → burayı kalibrasyonla bulun
# DROP_THRESHOLD_M = 0.5       # yarı metrelik tolerans

# # ----- 3) YOLO ve kamera başlatma -----
# model = YOLO('my_yolo_train/weights/best.pt')
# CLASS_NAMES = model.names
# cap = cv2.VideoCapture(0)

# prev_time = time.time()

# while True:
#     ret, frame = cap.read()
#     if not ret:
#         break

#     # FPS
#     curr_time = time.time()
#     fps = 1 / (curr_time - prev_time)
#     prev_time = curr_time

#     h_img, w_img = frame.shape[:2]
#     center_img = (w_img//2, h_img//2)

#     # Kare tespiti
#     results = model(frame, verbose=False)[0]
#     boxes = results.boxes.xyxy.cpu().numpy() if results.boxes.xyxy is not None else []
#     classes = results.boxes.cls.cpu().numpy() if results.boxes.cls is not None else []

#     for i, box in enumerate(boxes):
#         x1, y1, x2, y2 = map(int, box)
#         cls = int(classes[i]) if i < len(classes) else 0
#         label = CLASS_NAMES[cls]

#         # Şekil kontur vs. (sizin orijinal koddan kopyalandı)…
#         roi = frame[y1:y2, x1:x2]
#         if roi.size == 0: continue
#         gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
#         _, thresh = cv2.threshold(gray, 60, 255, cv2.THRESH_BINARY_INV+cv2.THRESH_OTSU)
#         contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
#         if not contours: continue
#         cnt = max(contours, key=cv2.contourArea)
#         if cv2.contourArea(cnt) < 0.2 * roi.shape[0] * roi.shape[1]: continue
#         approx = cv2.approxPolyDP(cnt, 0.04*cv2.arcLength(cnt,True), True)
#         if not (len(approx)==4 and cv2.isContourConvex(approx)): continue

#         # Piksel koordinat merkez
#         box_center_px = ((x1+x2)//2, (y1+y2)//2)
#         # Görüntü merkezini (0,0) kabul edip göreli piksel uzaklık
#         rel_x_px = box_center_px[0] - center_img[0]

#         # → Gerçek dünyadaki yatay mesafeye dönüştürme (metre)
#         rel_x_m = rel_x_px * METERS_PER_PIXEL_X

#         # Hesaplanan bırakma mesafesi
#         required_drop_dist = compute_drop_distance(ALTITUDE_M, HORIZONTAL_VELOCITY_MPS)

#         # Eğer hedef kare, bırakma mesafesine yakınsa DROP tetikle
#         if abs(rel_x_m - required_drop_dist) < DROP_THRESHOLD_M:
#             print(f"DROP: rel_x_m={rel_x_m:.2f} m, target={required_drop_dist:.2f} m")
#             # → Burada gerçek komutunuzu çağırın:
#             # send_drop_command_mavlink() veya GPIO.set_high() vb.
#             # örnek:
#             # send_drop_command({"cmd": "DROP"})
#             break

#         # Görselleştirme (isteğe bağlı)
#         color = (0,255,0)
#         cv2.circle(frame, box_center_px, 5, color, -1)
#         cv2.line(frame, box_center_px, center_img, color, 2)
#         cv2.putText(frame, f"{rel_x_m:.2f}m", (box_center_px[0]+5, box_center_px[1]),
#                     cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

#     # FPS ve gösterim
#     cv2.putText(frame, f"FPS: {fps:.1f}", (10,30),
#                 cv2.FONT_HERSHEY_SIMPLEX, 1, (0,255,0), 2)
#     cv2.imshow('Drop Test', frame)
#     if cv2.waitKey(1) & 0xFF == ord('q'):
#         break

# cap.release()
# cv2.destroyAllWindows()
